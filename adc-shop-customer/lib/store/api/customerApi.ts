import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import { getSession } from 'next-auth/react'
import type {
  CustomerResponse,
  CustomerShopSettings,
  CustomerMenuItem,
  MenuCategory,
  ShopFilters,
  MenuFilters,
} from '@/lib/services/customerApiClient'

// Base query with authentication
const baseQuery = fetchBaseQuery({
  baseUrl: '/api',
  prepareHeaders: async (headers, { getState }) => {
    // Get session for authentication
    const session = await getSession()

    if (session?.user) {
      headers.set('X-User-ID', session.user.id)
      headers.set('X-User-Email', session.user.email)
      headers.set('X-User-Role', session.user.role || 'customer')
      headers.set('X-Auth-Source', 'nextauth')
    }

    return headers
  },
})

export const customerApi = createApi({
  reducerPath: 'customerApi',
  baseQuery,
  tagTypes: ['Shop', 'MenuItem', 'MenuCategory', 'Cart', 'Order'],
  endpoints: (builder) => ({
    // Shop endpoints (Backend determines if authentication is required)
    getShops: builder.query<CustomerResponse<{ shops: CustomerShopSettings[] }>, ShopFilters>({
      query: (filters = {}) => {
        const params = new URLSearchParams()
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(item => params.append(key, item.toString()))
            } else {
              params.append(key, value.toString())
            }
          }
        })
        const queryString = params.toString()
        console.log('RTK Query - getShops query string:', queryString)
        return `shops?${queryString}`
      },
      providesTags: ['Shop'],
      // Force refetch when filters change by using a unique cache key
      serializeQueryArgs: ({ queryArgs }) => {
        return JSON.stringify(queryArgs)
      },
    }),

    getShop: builder.query<CustomerResponse<{ shop: CustomerShopSettings }>, string>({
      query: (shopId) => `shops/${shopId}`,
      providesTags: (result, error, shopId) => [{ type: 'Shop', id: shopId }],
    }),

    searchShops: builder.query<CustomerResponse<{ shops: CustomerShopSettings[] }>, { query: string; filters?: ShopFilters }>({
      query: ({ query, filters = {} }) => {
        const params = new URLSearchParams({ q: query })
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(item => params.append(key, item.toString()))
            } else {
              params.append(key, value.toString())
            }
          }
        })
        return `shops/search?${params.toString()}`
      },
      providesTags: ['Shop'],
    }),

    getPopularShops: builder.query<CustomerResponse<{ shops: CustomerShopSettings[] }>, ShopFilters>({
      query: (filters = {}) => {
        const params = new URLSearchParams()
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(item => params.append(key, item.toString()))
            } else {
              params.append(key, value.toString())
            }
          }
        })
        return `shops/popular?${params.toString()}`
      },
      providesTags: ['Shop'],
    }),

    getNearbyShops: builder.query<CustomerResponse<{ shops: CustomerShopSettings[] }>, {
      latitude: number;
      longitude: number;
      radius?: number;
      filters?: ShopFilters;
    }>({
      query: ({ latitude, longitude, radius = 5, filters = {} }) => {
        const params = new URLSearchParams({
          latitude: latitude.toString(),
          longitude: longitude.toString(),
          radius: radius.toString()
        })
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(item => params.append(key, item.toString()))
            } else {
              params.append(key, value.toString())
            }
          }
        })
        return `shops/nearby?${params.toString()}`
      },
      providesTags: ['Shop'],
    }),

    getShopsByCategory: builder.query<CustomerResponse<{ shops: CustomerShopSettings[] }>, { category: string; filters?: ShopFilters }>({
      query: ({ category, filters = {} }) => {
        const params = new URLSearchParams()
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(item => params.append(key, item.toString()))
            } else {
              params.append(key, value.toString())
            }
          }
        })
        return `shops/category/${category}?${params.toString()}`
      },
      providesTags: ['Shop'],
    }),

    getShopStatus: builder.query<CustomerResponse<{ is_open: boolean }>, string>({
      query: (shopId) => `shops/${shopId}/status`,
      providesTags: (result, error, shopId) => [{ type: 'Shop', id: `${shopId}-status` }],
    }),

    getShopBySlug: builder.query<CustomerResponse<{ shop: CustomerShopSettings }>, string>({
      query: (shopSlug) => `shops/slug/${shopSlug}`,
      providesTags: (result, error, shopSlug) => [{ type: 'Shop', id: shopSlug }],
    }),

    getShopBranchBySlug: builder.query<CustomerResponse<{ branch: any }>, {
      shopSlug: string;
      branchSlug: string;
    }>({
      query: ({ shopSlug, branchSlug }) => `shops/slug/${shopSlug}/branches/slug/${branchSlug}`,
      providesTags: (result, error, { shopSlug, branchSlug }) => [
        { type: 'Shop', id: `${shopSlug}-${branchSlug}` }
      ],
    }),

    // Menu endpoints (Backend determines if authentication is required)
    getMenuItems: builder.query<CustomerResponse<{ items: CustomerMenuItem[]; categories: MenuCategory[] }>, {
      shopId: string;
      filters?: MenuFilters;
    }>({
      query: ({ shopId, filters = {} }) => {
        const params = new URLSearchParams()
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(item => params.append(key, item.toString()))
            } else {
              params.append(key, value.toString())
            }
          }
        })
        return `shops/${shopId}/menu?${params.toString()}`
      },
      providesTags: (result, error, { shopId }) => [
        { type: 'MenuItem', id: shopId },
        { type: 'MenuCategory', id: shopId },
      ],
    }),

    getMenuItem: builder.query<CustomerResponse<{ item: CustomerMenuItem }>, string>({
      query: (itemId) => `menu/items/${itemId}`,
      providesTags: (result, error, itemId) => [{ type: 'MenuItem', id: itemId }],
    }),

    getPopularMenuItems: builder.query<CustomerResponse<{ items: CustomerMenuItem[] }>, {
      shopId: string;
      filters?: MenuFilters;
    }>({
      query: ({ shopId, filters = {} }) => {
        const params = new URLSearchParams()
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(item => params.append(key, item.toString()))
            } else {
              params.append(key, value.toString())
            }
          }
        })
        return `shops/${shopId}/menu/popular?${params.toString()}`
      },
      providesTags: (result, error, { shopId }) => [{ type: 'MenuItem', id: `${shopId}-popular` }],
    }),

    // Menu endpoints using shop slug
    getMenuItemsBySlug: builder.query<CustomerResponse<{ items: CustomerMenuItem[]; categories: MenuCategory[] }>, {
      shopSlug: string;
      filters?: MenuFilters;
    }>({
      query: ({ shopSlug, filters = {} }) => {
        const params = new URLSearchParams()
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(item => params.append(key, item.toString()))
            } else {
              params.append(key, value.toString())
            }
          }
        })
        return `shops/slug/${shopSlug}/menu?${params.toString()}`
      },
      providesTags: (result, error, { shopSlug }) => [
        { type: 'MenuItem', id: shopSlug },
        { type: 'MenuCategory', id: shopSlug },
      ],
    }),

    // Menu endpoints using shop and branch slug (new branch-specific endpoint)
    getMenuItemsByBranchSlug: builder.query<CustomerResponse<{ items: CustomerMenuItem[]; categories: MenuCategory[] }>, {
      shopSlug: string;
      branchSlug: string;
      filters?: MenuFilters;
    }>({
      query: ({ shopSlug, branchSlug, filters = {} }) => {
        const params = new URLSearchParams()
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(item => params.append(key, item.toString()))
            } else {
              params.append(key, value.toString())
            }
          }
        })
        return `shops/slug/${shopSlug}/branches/slug/${branchSlug}/menu?${params.toString()}`
      },
      providesTags: (result, error, { shopSlug, branchSlug }) => [
        { type: 'MenuItem', id: `${shopSlug}-${branchSlug}` },
        { type: 'MenuCategory', id: `${shopSlug}-${branchSlug}` },
      ],
    }),

    getNewMenuItems: builder.query<CustomerResponse<{ items: CustomerMenuItem[] }>, {
      shopId: string;
      filters?: MenuFilters;
    }>({
      query: ({ shopId, filters = {} }) => {
        const params = new URLSearchParams()
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(item => params.append(key, item.toString()))
            } else {
              params.append(key, value.toString())
            }
          }
        })
        return `shops/${shopId}/menu/new?${params.toString()}`
      },
      providesTags: (result, error, { shopId }) => [{ type: 'MenuItem', id: `${shopId}-new` }],
    }),

    getVegetarianItems: builder.query<CustomerResponse<{ items: CustomerMenuItem[] }>, {
      shopId: string;
      filters?: MenuFilters;
    }>({
      query: ({ shopId, filters = {} }) => {
        const params = new URLSearchParams()
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(item => params.append(key, item.toString()))
            } else {
              params.append(key, value.toString())
            }
          }
        })
        return `shops/${shopId}/menu/vegetarian?${params.toString()}`
      },
      providesTags: (result, error, { shopId }) => [{ type: 'MenuItem', id: `${shopId}-vegetarian` }],
    }),

    searchMenuItems: builder.query<CustomerResponse<{ items: CustomerMenuItem[] }>, {
      shopId: string;
      query: string;
      filters?: MenuFilters;
    }>({
      query: ({ shopId, query, filters = {} }) => {
        const params = new URLSearchParams({ q: query })
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(item => params.append(key, item.toString()))
            } else {
              params.append(key, value.toString())
            }
          }
        })
        return `shops/${shopId}/menu/search?${params.toString()}`
      },
      providesTags: (result, error, { shopId }) => [{ type: 'MenuItem', id: `${shopId}-search` }],
    }),

    getMenuCategories: builder.query<CustomerResponse<{ categories: MenuCategory[] }>, string>({
      query: (shopId) => `shops/${shopId}/menu/categories`,
      providesTags: (result, error, shopId) => [{ type: 'MenuCategory', id: shopId }],
    }),

    getItemsByCategory: builder.query<CustomerResponse<{ items: CustomerMenuItem[] }>, {
      shopId: string;
      categoryId: string;
      filters?: MenuFilters;
    }>({
      query: ({ shopId, categoryId, filters = {} }) => {
        const params = new URLSearchParams()
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(item => params.append(key, item.toString()))
            } else {
              params.append(key, value.toString())
            }
          }
        })
        return `shops/${shopId}/menu/categories/${categoryId}?${params.toString()}`
      },
      providesTags: (result, error, { shopId, categoryId }) => [
        { type: 'MenuItem', id: `${shopId}-${categoryId}` },
      ],
    }),

    // Get shop filter options
    getShopFilterOptions: builder.query<CustomerResponse<{
      cuisine_types: string[];
      price_ranges: string[];
      features: string[];
    }>, void>({
      query: () => 'shops/filter-options',
      providesTags: ['Shop'],
    }),

    // Cart endpoints
    getCart: builder.query<CustomerResponse<{ items: any[] }>, void>({
      query: () => 'cart',
      providesTags: ['Cart'],
    }),

    addToCart: builder.mutation<CustomerResponse<any>, { item: any; quantity?: number }>({
      query: ({ item, quantity = 1 }) => ({
        url: 'cart/add',
        method: 'POST',
        body: { item, quantity },
      }),
      invalidatesTags: ['Cart'],
    }),

    updateCartQuantity: builder.mutation<CustomerResponse<any>, { item_id: string; quantity: number }>({
      query: ({ item_id, quantity }) => ({
        url: 'cart/update',
        method: 'PUT',
        body: { item_id, quantity },
      }),
      invalidatesTags: ['Cart'],
    }),

    removeFromCart: builder.mutation<CustomerResponse<any>, { item_id: string }>({
      query: ({ item_id }) => ({
        url: 'cart/remove',
        method: 'DELETE',
        body: { item_id },
      }),
      invalidatesTags: ['Cart'],
    }),

    clearCart: builder.mutation<CustomerResponse<any>, void>({
      query: () => ({
        url: 'cart/clear',
        method: 'DELETE',
      }),
      invalidatesTags: ['Cart'],
    }),

    clearBranchCart: builder.mutation<CustomerResponse<any>, { shop_slug: string; branch_slug: string }>({
      query: ({ shop_slug, branch_slug }) => ({
        url: 'cart/clear-branch',
        method: 'DELETE',
        body: { shop_slug, branch_slug },
      }),
      invalidatesTags: ['Cart'],
    }),

    syncCart: builder.mutation<CustomerResponse<any>, void>({
      query: () => ({
        url: 'cart/sync',
        method: 'POST',
      }),
      invalidatesTags: ['Cart'],
    }),

    // Order endpoints
    getOrdersByCustomer: builder.query<CustomerResponse<{ orders: any[] }>, {
      customerPhone?: string;
      limit?: number;
      page?: number;
      status?: string;
      branchId?: string;
    }>({
      query: (params = {}) => {
        const searchParams = new URLSearchParams()
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            searchParams.append(key, value.toString())
          }
        })
        return `orders/customer?${searchParams.toString()}`
      },
      providesTags: ['Order'],
    }),

    getOrderById: builder.query<CustomerResponse<{ order: any }>, string>({
      query: (orderId) => `orders/${orderId}`,
      providesTags: (result, error, orderId) => [{ type: 'Order', id: orderId }],
    }),

    getOrderByNumber: builder.query<CustomerResponse<{ order: any }>, string>({
      query: (orderNumber) => `orders/number/${orderNumber}`,
      providesTags: (result, error, orderNumber) => [{ type: 'Order', id: orderNumber }],
    }),

    updateOrderStatus: builder.mutation<CustomerResponse<{ order: any }>, {
      orderId: string;
      status: string;
    }>({
      query: ({ orderId, status }) => ({
        url: `orders/${orderId}/status`,
        method: 'PUT',
        body: { status },
      }),
      invalidatesTags: (result, error, { orderId }) => [
        { type: 'Order', id: orderId },
        'Order',
      ],
    }),

    createOrderWithPayment: builder.mutation<CustomerResponse<{
      order: any;
      payment_intent?: {
        client_secret: string;
        payment_intent_id: string;
      };
      connected_account_id?: string;
      requires_payment: boolean;
      estimated_time?: number;
    }>, {
      branch_id: string;
      table_id?: string;
      customer_name: string;
      customer_phone: string;
      customer_email?: string;
      order_type: 'dine_in' | 'takeaway' | 'delivery';
      items: Array<{
        menu_item_id: string;
        quantity: number;
        unit_price: number;
        customizations?: any[];
        special_requests?: string;
      }>;
      notes?: string;
      payment_method: 'card' | 'cash' | 'stripe_connect';
      metadata?: Record<string, string>;
    }>({
      query: (orderData) => ({
        url: 'orders/create-with-payment',
        method: 'POST',
        body: orderData,
      }),
      invalidatesTags: ['Order', 'Cart'],
    }),
  }),
})

// Export hooks for usage in functional components
export const {
  useGetShopsQuery,
  useGetShopQuery,
  useSearchShopsQuery,
  useGetPopularShopsQuery,
  useGetNearbyShopsQuery,
  useGetShopsByCategoryQuery,
  useGetShopStatusQuery,
  useGetShopBySlugQuery,
  useGetShopBranchBySlugQuery,
  useGetMenuItemsQuery,
  useGetMenuItemsBySlugQuery,
  useGetMenuItemsByBranchSlugQuery,
  useGetMenuItemQuery,
  useGetPopularMenuItemsQuery,
  useGetNewMenuItemsQuery,
  useGetVegetarianItemsQuery,
  useSearchMenuItemsQuery,
  useGetMenuCategoriesQuery,
  useGetItemsByCategoryQuery,
  useGetShopFilterOptionsQuery,
  useGetCartQuery,
  useAddToCartMutation,
  useUpdateCartQuantityMutation,
  useRemoveFromCartMutation,
  useClearCartMutation,
  useClearBranchCartMutation,
  useSyncCartMutation,
  useGetOrdersByCustomerQuery,
  useGetOrderByIdQuery,
  useGetOrderByNumberQuery,
  useUpdateOrderStatusMutation,
  useCreateOrderWithPaymentMutation,
} = customerApi
