// Session Service for guest user management
// Handles session ID generation and persistence for guest users

export class SessionService {
  private sessionId: string | null = null;
  private readonly SESSION_KEY = 'cart_session_id'; // Reuse cart session for consistency

  constructor() {
    // Initialize session ID for guest users
    if (typeof window !== 'undefined') {
      this.sessionId = this.getOrCreateSessionId();
    }
  }

  /**
   * Get existing session ID or create a new one for guest users
   */
  getOrCreateSessionId(): string {
    if (typeof window === 'undefined') return '';
    
    const stored = localStorage.getItem(this.SESSION_KEY);
    if (stored) {
      this.sessionId = stored;
      return stored;
    }

    // Generate new session ID
    const newSessionId = `guest_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    localStorage.setItem(this.SESSION_KEY, newSessionId);
    this.sessionId = newSessionId;
    return newSessionId;
  }

  /**
   * Get current session ID
   */
  getSessionId(): string | null {
    return this.sessionId;
  }

  /**
   * Clear session ID (used when user logs in)
   */
  clearSessionId(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(this.SESSION_KEY);
    }
    this.sessionId = null;
  }

  /**
   * Check if current user is a guest (has session ID but no authentication)
   */
  isGuestUser(): boolean {
    return !!this.sessionId && typeof window !== 'undefined';
  }

  /**
   * Generate a new session ID (useful for creating new guest sessions)
   */
  generateNewSessionId(): string {
    const newSessionId = `guest_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    if (typeof window !== 'undefined') {
      localStorage.setItem(this.SESSION_KEY, newSessionId);
    }
    this.sessionId = newSessionId;
    return newSessionId;
  }
}

// Export singleton instance
export const sessionService = new SessionService();
